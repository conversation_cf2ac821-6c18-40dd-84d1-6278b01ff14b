"""
Management command to create realistic curator user accounts for seed pins.

This command generates diverse curator accounts with believable usernames,
profiles, and music preferences for different cities and regions.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.auth import get_user_model

from seeding.models import CuratorAccount
from seeding.services.user_generation_service import UserGenerationService
from seeding.services.profile_picture_service import ProfilePictureService

User = get_user_model()


class Command(BaseCommand):
    help = 'Create realistic curator user accounts for seed pins'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=5000,
            help='Number of curator accounts to create (default: 5000)'
        )
        parser.add_argument(
            '--city',
            type=str,
            help='Create curators for a specific city'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation even if curators already exist'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of curators to create in each batch (default: 100)'
        )
    
    def handle(self, *args, **options):
        count = options['count']
        city = options.get('city')
        force = options['force']
        batch_size = options['batch_size']

        # Check if curators already exist
        existing_count = CuratorAccount.objects.count()
        if existing_count > 0 and not force:
            self.stdout.write(
                self.style.WARNING(
                    f'Found {existing_count} existing curator accounts. '
                    'Use --force to create additional curators.'
                )
            )
            return

        self.stdout.write(f'Creating {count} curator accounts in batches of {batch_size}...')

        try:
            created_curators = self._create_curator_accounts_in_batches(count, city, batch_size)

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created {len(created_curators)} curator accounts'
                )
            )

            # Display sample of created curators
            sample_size = min(10, len(created_curators))
            self.stdout.write(f'Sample of created curators (showing {sample_size} of {len(created_curators)}):')
            for curator in created_curators[:sample_size]:
                self.stdout.write(
                    f'  • {curator.user.username} ({curator.get_persona_type_display()})'
                )

            if len(created_curators) > sample_size:
                self.stdout.write(f'  ... and {len(created_curators) - sample_size} more')

        except Exception as e:
            raise CommandError(f'Error creating curator accounts: {str(e)}')

    def _create_curator_accounts_in_batches(self, total_count, target_city=None, batch_size=100):
        """Create curator accounts in batches for better performance"""
        all_created_curators = []

        for batch_start in range(0, total_count, batch_size):
            batch_end = min(batch_start + batch_size, total_count)
            batch_count = batch_end - batch_start

            self.stdout.write(f'Creating batch {batch_start//batch_size + 1}: curators {batch_start + 1}-{batch_end}')

            try:
                with transaction.atomic():
                    batch_curators = self._create_curator_accounts(batch_count, target_city)
                    all_created_curators.extend(batch_curators)

                    self.stdout.write(f'  ✓ Created {len(batch_curators)} curators in this batch')

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating batch {batch_start//batch_size + 1}: {str(e)}')
                )
                continue

        return all_created_curators

    def _create_curator_accounts(self, count, target_city=None):
        """Create diverse curator accounts with realistic profiles using Faker"""

        # Initialize services
        user_gen_service = UserGenerationService()

        # Simplified curator persona configurations for efficient generation
        curator_persona_configs = [
            {
                'persona_type': 'indie_explorer',
                'preferred_genres': ['indie', 'alternative', 'folk', 'shoegaze'],
                'preferred_locations': ['cafe', 'venue', 'campus', 'park'],
                'cities': ['seattle', 'portland', 'austin', 'brooklyn']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['jazz', 'acoustic', 'chill', 'bossa nova'],
                'preferred_locations': ['cafe', 'park'],
                'cities': ['san francisco', 'seattle', 'new york', 'chicago']
            },
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['rock', 'electronic', 'hip-hop', 'punk'],
                'preferred_locations': ['venue', 'landmark'],
                'cities': ['nashville', 'austin', 'los angeles', 'chicago']
            },
            {
                'persona_type': 'campus_curator',
                'preferred_genres': ['lo-fi', 'indie', 'electronic', 'ambient'],
                'preferred_locations': ['campus', 'cafe', 'park'],
                'cities': ['boston', 'berkeley', 'ann arbor', 'cambridge']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['pop', 'hip-hop', 'electronic', 'world'],
                'preferred_locations': ['landmark', 'transit', 'venue', 'cafe'],
                'cities': ['new york', 'los angeles', 'chicago', 'miami']
            },
            {
                'persona_type': 'music_historian',
                'preferred_genres': ['classic rock', 'blues', 'jazz', 'soul'],
                'preferred_locations': ['landmark', 'venue', 'park'],
                'cities': ['nashville', 'memphis', 'detroit', 'new orleans']
            },
            {
                'persona_type': 'genre_specialist',
                'preferred_genres': ['electronic', 'ambient', 'experimental', 'techno'],
                'preferred_locations': ['venue', 'campus', 'landmark'],
                'cities': ['berlin', 'detroit', 'london', 'montreal']
            },
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['local', 'folk', 'country', 'americana'],
                'preferred_locations': ['landmark', 'park', 'venue'],
                'cities': ['nashville', 'austin', 'portland', 'asheville']
            },
            # Additional diverse personas for variety
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['house', 'techno', 'ambient', 'downtempo'],
                'preferred_locations': ['venue', 'transit'],
                'cities': ['berlin', 'new york', 'los angeles', 'miami']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['folk', 'ambient', 'acoustic', 'world'],
                'preferred_locations': ['park', 'landmark'],
                'cities': ['portland', 'denver', 'seattle', 'san francisco']
            },
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['world', 'reggae', 'latin', 'afrobeat'],
                'preferred_locations': ['landmark', 'venue', 'cafe'],
                'cities': ['new york', 'los angeles', 'miami', 'toronto']
            },
        ]

        # Filter by city if specified
        if target_city:
            curator_persona_configs = [
                config for config in curator_persona_configs
                if target_city.lower() in [city.lower() for city in config['cities']]
            ]

        # Ensure we have enough configurations by cycling through them
        if len(curator_persona_configs) == 0:
            raise CommandError("No persona configurations available for the specified city")

        created_curators = []
        existing_usernames = set(User.objects.values_list('username', flat=True))

        for i in range(count):
            try:
                # Cycle through configurations
                config = curator_persona_configs[i % len(curator_persona_configs)]

                # Generate diverse user profile using Faker
                user_profile = user_gen_service.generate_user_profile(
                    persona_type=config['persona_type'],
                    preferred_genres=config['preferred_genres'],
                    preferred_locations=config['preferred_locations'],
                    assigned_cities=config['cities']
                )

                # Ensure username uniqueness
                unique_username = user_gen_service.ensure_username_uniqueness(
                    user_profile['username'],
                    existing_usernames
                )
                existing_usernames.add(unique_username)

                # Create user with generated profile
                curator_user = User.objects.create_user(
                    username=unique_username,
                    email=user_profile['email'],
                    first_name=user_profile['first_name'],
                    last_name=user_profile['last_name'],
                    bio=user_profile['bio'],
                    profile_pic=user_profile['profile_pic']
                )

                # Create curator profile
                curator = CuratorAccount.objects.create(
                    user=curator_user,
                    persona_type=config['persona_type'],
                    preferred_genres=config['preferred_genres'],
                    preferred_locations=config['preferred_locations'],
                    assigned_cities=config['cities']
                )

                created_curators.append(curator)

                # Log progress every 50 curators
                if (i + 1) % 50 == 0:
                    self.stdout.write(f'  Created {i + 1}/{count} curators...')

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating curator {i + 1}: {str(e)}')
                )
                continue

        return created_curators
