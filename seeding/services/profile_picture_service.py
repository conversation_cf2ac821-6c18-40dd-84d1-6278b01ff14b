"""
Profile picture service for curator accounts.

This service manages profile pictures for curator accounts, providing
realistic profile pictures using Faker and various avatar services.
Uses pravatar.cc for diverse, realistic profile pictures.
"""

import logging
import random
import hashlib
import uuid
from typing import Dict, List, Optional
from django.conf import settings
from django.core.files.base import ContentFile
import requests
from faker import Faker

logger = logging.getLogger(__name__)


class ProfilePictureService:
    """
    Service for managing curator profile pictures using Faker and realistic avatar services.
    """

    # Avatar services for realistic profile pictures
    AVATAR_SERVICES = {
        'pravatar': {
            'base_url': 'https://i.pravatar.cc',
            'supports_gender': True,
            'supports_age': False,
            'default_size': 150
        },
        'robohash': {
            'base_url': 'https://robohash.org',
            'supports_gender': False,
            'supports_age': False,
            'default_size': 150
        },
        'dicebear': {
            'base_url': 'https://api.dicebear.com/7.x',
            'styles': ['avataaars', 'big-smile', 'bottts', 'identicon', 'initials', 'lorelei', 'micah', 'miniavs', 'open-peeps', 'personas', 'pixel-art'],
            'supports_gender': True,
            'supports_age': False,
            'default_size': 150
        }
    }

    def __init__(self):
        self.image_size = getattr(settings, 'CURATOR_PROFILE_PIC_SIZE', 150)
        self.faker = Faker()

    def generate_realistic_avatar_url(self, unique_identifier: str, gender: Optional[str] = None) -> str:
        """
        Generate a realistic avatar URL using various services and Faker.

        Args:
            unique_identifier: Unique identifier for consistent avatar generation
            gender: Optional gender for avatar generation ('male', 'female', or None for random)

        Returns:
            Avatar URL from a random service
        """
        # Create a hash of the identifier to ensure consistency
        hash_object = hashlib.md5(unique_identifier.encode())
        hash_hex = hash_object.hexdigest()

        # Use hash to determine which service to use consistently
        service_choice = int(hash_hex[:2], 16) % 3

        if service_choice == 0:
            # Use pravatar.cc for realistic faces
            return self._generate_pravatar_url(hash_hex, gender)
        elif service_choice == 1:
            # Use DiceBear for stylized avatars
            return self._generate_dicebear_url(hash_hex, gender)
        else:
            # Use RoboHash for unique robot avatars
            return self._generate_robohash_url(hash_hex)

    def _generate_pravatar_url(self, hash_hex: str, gender: Optional[str] = None) -> str:
        """Generate a pravatar.cc URL"""
        url = f"https://i.pravatar.cc/{self.image_size}?u={hash_hex}"

        # Add gender parameter if specified
        if gender in ['male', 'female']:
            url += f"&gender={gender}"

        return url

    def _generate_dicebear_url(self, hash_hex: str, gender: Optional[str] = None) -> str:
        """Generate a DiceBear avatar URL"""
        # Choose a random style
        style = random.choice(self.AVATAR_SERVICES['dicebear']['styles'])
        url = f"https://api.dicebear.com/7.x/{style}/svg?seed={hash_hex}&size={self.image_size}"

        # Add gender parameter if specified and supported
        if gender in ['male', 'female'] and style in ['avataaars', 'big-smile', 'lorelei', 'micah', 'open-peeps', 'personas']:
            url += f"&gender={gender}"

        return url

    def _generate_robohash_url(self, hash_hex: str) -> str:
        """Generate a RoboHash avatar URL"""
        return f"https://robohash.org/{hash_hex}?size={self.image_size}x{self.image_size}"

    def assign_profile_picture(self, curator_account) -> Optional[str]:
        """
        Assign a realistic profile picture to a curator account using Faker and various avatar services.

        Args:
            curator_account: CuratorAccount instance

        Returns:
            URL of the assigned profile picture or None if failed
        """
        try:
            # Generate a unique identifier for this curator
            unique_id = f"{curator_account.user.username}_{curator_account.persona_type}_{curator_account.id}"

            # Generate a random gender for diversity (or use None for random)
            gender = random.choice(['male', 'female', None])

            # Generate profile picture URL using realistic avatar services
            picture_url = self.generate_realistic_avatar_url(unique_id, gender)

            # Update the user's profile picture
            curator_account.user.profile_pic = picture_url
            curator_account.user.save(update_fields=['profile_pic'])

            logger.info(
                f"Assigned realistic profile picture to curator {curator_account.user.username}: {picture_url}"
            )

            return picture_url

        except Exception as e:
            logger.error(f"Error assigning profile picture to curator {curator_account.user.username}: {str(e)}")

        return None

    def generate_diverse_avatar_batch(self, count: int) -> List[str]:
        """
        Generate a batch of diverse avatar URLs for bulk operations.

        Args:
            count: Number of avatars to generate

        Returns:
            List of avatar URLs
        """
        avatars = []
        for i in range(count):
            # Generate unique identifier
            unique_id = f"batch_{i}_{self.faker.uuid4()}"

            # Random gender for diversity
            gender = random.choice(['male', 'female', None])

            # Generate avatar URL
            avatar_url = self.generate_realistic_avatar_url(unique_id, gender)
            avatars.append(avatar_url)

        return avatars

    def rotate_profile_pictures(self, curator_accounts: List) -> int:
        """
        Rotate profile pictures for a list of curator accounts to maintain freshness.

        Args:
            curator_accounts: List of CuratorAccount instances

        Returns:
            Number of profile pictures successfully rotated
        """
        rotated_count = 0

        for curator in curator_accounts:
            try:
                # Only rotate if curator has been used recently
                if hasattr(curator, 'last_used') and curator.last_used and hasattr(curator, 'pins_created') and curator.pins_created > 5:
                    new_url = self.assign_profile_picture(curator)
                    if new_url:
                        rotated_count += 1

            except Exception as e:
                logger.error(f"Error rotating profile picture for {curator.user.username}: {str(e)}")
                continue

        logger.info(f"Rotated {rotated_count} curator profile pictures")
        return rotated_count

    def validate_picture_url(self, url: str) -> bool:
        """
        Validate that a profile picture URL is accessible.

        Args:
            url: Profile picture URL to validate

        Returns:
            True if URL is accessible, False otherwise
        """
        try:
            response = requests.head(url, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"Profile picture URL validation failed for {url}: {str(e)}")
            return False

    def get_avatar_service_info(self) -> Dict:
        """Get information about available avatar services"""
        return self.AVATAR_SERVICES

    def list_available_services(self) -> List[str]:
        """List all available avatar services"""
        return list(self.AVATAR_SERVICES.keys())


# Utility functions for management commands
def assign_pictures_to_all_curators():
    """Assign realistic profile pictures to all curator accounts that don't have them"""
    from seeding.models import CuratorAccount

    service = ProfilePictureService()
    assigned_count = 0

    curators_without_pictures = CuratorAccount.objects.filter(
        user__profile_pic__isnull=True
    ).select_related('user')

    for curator in curators_without_pictures:
        if service.assign_profile_picture(curator):
            assigned_count += 1

    return assigned_count


def rotate_all_curator_pictures():
    """Rotate profile pictures for all active curators"""
    from seeding.models import CuratorAccount

    service = ProfilePictureService()
    active_curators = CuratorAccount.objects.filter(
        is_active=True
    ).select_related('user')

    return service.rotate_profile_pictures(list(active_curators))
