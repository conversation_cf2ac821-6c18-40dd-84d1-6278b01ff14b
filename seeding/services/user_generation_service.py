"""
User generation service for creating diverse, realistic user profiles for seeding.

This service provides:
- Diverse name generation using Faker across multiple locales
- Realistic username generation with variety
- Avatar URL generation using pravatar.cc
- Comprehensive user profile creation for curator accounts
"""

import random
from typing import Dict, List, Tuple
import logging
from faker import Faker

logger = logging.getLogger(__name__)


class UserGenerationService:
    """Service for generating diverse, realistic user profiles using Faker"""

    # Diverse locales for realistic name generation
    LOCALES = [
        'en_US',    # English (US)
        'en_GB',    # English (UK)
        'es_ES',    # Spanish (Spain)
        'es_MX',    # Spanish (Mexico)
        'fr_FR',    # French (France)
        'de_DE',    # German (Germany)
        'it_IT',    # Italian (Italy)
        'pt_BR',    # Portuguese (Brazil)
        'ja_JP',    # Japanese (Japan)
        'ko_KR',    # Korean (South Korea)
        'zh_CN',    # Chinese (China)
        'hi_IN',    # Hindi (India)
        'ar_SA',    # Arabic (Saudi Arabia)
        'ru_RU',    # Russian (Russia)
        'nl_NL',    # Dutch (Netherlands)
        'sv_SE',    # Swedish (Sweden)
        'no_NO',    # Norwegian (Norway)
        'da_DK',    # Danish (Denmark)
        'fi_FI',    # Finnish (Finland)
        'pl_PL',    # Polish (Poland)
        'tr_TR',    # Turkish (Turkey)
        'he_IL',    # Hebrew (Israel)
        'th_TH',    # Thai (Thailand)
        'vi_VN',    # Vietnamese (Vietnam)
        'id_ID',    # Indonesian (Indonesia)
        'ms_MY',    # Malay (Malaysia)
        'tl_PH',    # Filipino (Philippines)
        'uk_UA',    # Ukrainian (Ukraine)
        'cs_CZ',    # Czech (Czech Republic)
        'hu_HU',    # Hungarian (Hungary)
        'ro_RO',    # Romanian (Romania)
        'bg_BG',    # Bulgarian (Bulgaria)
        'hr_HR',    # Croatian (Croatia)
        'sk_SK',    # Slovak (Slovakia)
        'sl_SI',    # Slovenian (Slovenia)
        'et_EE',    # Estonian (Estonia)
        'lv_LV',    # Latvian (Latvia)
        'lt_LT',    # Lithuanian (Lithuania)
        'mt_MT',    # Maltese (Malta)
        'el_GR',    # Greek (Greece)
        'mk_MK',    # Macedonian (North Macedonia)
        'sq_AL',    # Albanian (Albania)
        'sr_RS',    # Serbian (Serbia)
        'bs_BA',    # Bosnian (Bosnia and Herzegovina)
        'me_ME',    # Montenegrin (Montenegro)
        'is_IS',    # Icelandic (Iceland)
        'ga_IE',    # Irish (Ireland)
        'cy_GB',    # Welsh (Wales)
        'eu_ES',    # Basque (Spain)
        'ca_ES',    # Catalan (Spain)
        'gl_ES',    # Galician (Spain)
        'pt_PT',    # Portuguese (Portugal)
        'fa_IR',    # Persian (Iran)
        'ur_PK',    # Urdu (Pakistan)
        'bn_BD',    # Bengali (Bangladesh)
        'ta_IN',    # Tamil (India)
        'te_IN',    # Telugu (India)
        'ml_IN',    # Malayalam (India)
        'kn_IN',    # Kannada (India)
        'gu_IN',    # Gujarati (India)
        'pa_IN',    # Punjabi (India)
        'or_IN',    # Odia (India)
        'as_IN',    # Assamese (India)
        'ne_NP',    # Nepali (Nepal)
        'si_LK',    # Sinhala (Sri Lanka)
        'my_MM',    # Burmese (Myanmar)
        'km_KH',    # Khmer (Cambodia)
        'lo_LA',    # Lao (Laos)
        'ka_GE',    # Georgian (Georgia)
        'hy_AM',    # Armenian (Armenia)
        'az_AZ',    # Azerbaijani (Azerbaijan)
        'kk_KZ',    # Kazakh (Kazakhstan)
        'ky_KG',    # Kyrgyz (Kyrgyzstan)
        'uz_UZ',    # Uzbek (Uzbekistan)
        'tg_TJ',    # Tajik (Tajikistan)
        'tk_TM',    # Turkmen (Turkmenistan)
        'mn_MN',    # Mongolian (Mongolia)
        'sw_KE',    # Swahili (Kenya)
        'am_ET',    # Amharic (Ethiopia)
        'zu_ZA',    # Zulu (South Africa)
        'af_ZA',    # Afrikaans (South Africa)
        'xh_ZA',    # Xhosa (South Africa)
        'st_ZA',    # Sotho (South Africa)
        'tn_ZA',    # Tswana (South Africa)
        'ss_ZA',    # Swazi (South Africa)
        'nr_ZA',    # Ndebele (South Africa)
        've_ZA',    # Venda (South Africa)
        'ts_ZA',    # Tsonga (South Africa)
    ]

    def __init__(self):
        """Initialize the service with multiple Faker instances for different locales"""
        self.fakers = {}

        # Create Faker instances for each locale, with fallback for unsupported locales
        for locale in self.LOCALES:
            try:
                self.fakers[locale] = Faker(locale)
            except AttributeError:
                # Fallback to en_US for unsupported locales
                self.fakers[locale] = Faker('en_US')

        # Default faker for fallback
        self.default_faker = Faker('en_US')

        # Initialize profile picture service for realistic avatars
        # Import here to avoid circular imports
        from .profile_picture_service import ProfilePictureService
        self.profile_pic_service = ProfilePictureService()

    # Username patterns for generating realistic usernames
    USERNAME_PATTERNS = [
        '{first_name}_{last_initial}',
        '{first_name}_{number}',
        '{first_name}_{adjective}',
        '{adjective}_{first_name}',
        '{first_name}.{last_name}',
        '{first_initial}{last_name}',
        '{first_name}{number}',
        '{first_name}_{year}',
        '{first_name}_{word}',
        '{word}_{first_name}',
        '{first_name}_{last_initial}{number}',
        '{first_initial}{last_name}{number}',
        '{first_name}.{word}',
        '{word}.{first_name}',
        '{first_name}_{color}',
        '{color}_{first_name}',
        '{first_name}_{nature}',
        '{nature}_{first_name}'
    ]

    def generate_diverse_name(self) -> Tuple[str, str]:
        """
        Generate a diverse first and last name combination using Faker.

        Returns:
            Tuple of (first_name, last_name)
        """
        # Select a random locale for diversity
        locale = random.choice(self.LOCALES)
        faker = self.fakers.get(locale, self.default_faker)

        # Generate name using the selected locale
        first_name = faker.first_name()
        last_name = faker.last_name()

        return first_name, last_name

    def generate_realistic_username(self, first_name: str, last_name: str) -> str:
        """
        Generate a realistic username based on name and common patterns using Faker.

        Args:
            first_name: User's first name
            last_name: User's last name

        Returns:
            Generated username
        """
        faker = self.default_faker

        # Select a random pattern
        pattern = random.choice(self.USERNAME_PATTERNS)

        # Prepare variables for pattern substitution
        variables = {
            'first_name': first_name.lower(),
            'last_name': last_name.lower(),
            'first_initial': first_name[0].lower(),
            'last_initial': last_name[0].lower(),
            'number': str(random.randint(1, 999)),
            'adjective': faker.word(),
            'word': faker.word(),
            'color': faker.color_name(),
            'nature': random.choice(['river', 'ocean', 'mountain', 'forest', 'desert', 'valley', 'meadow', 'grove']),
            'year': str(random.randint(1990, 2010))
        }

        # Generate username
        username = pattern.format(**variables)

        # Clean username (remove spaces, special characters)
        username = ''.join(c for c in username if c.isalnum() or c in '._-')

        # Add random suffix if username might be too common
        if random.random() < 0.3:  # 30% chance
            username += str(random.randint(1, 99))

        return username



    def generate_bio(self, persona_type: str, first_name: str) -> str:
        """
        Generate a realistic bio based on persona type and name using Faker.

        Args:
            persona_type: The curator persona type
            first_name: User's first name for personalization

        Returns:
            Generated bio text
        """
        faker = self.default_faker

        # Base bio templates with placeholders for dynamic content
        bio_templates = {
            'indie_explorer': [
                f"Hey, I'm {first_name}! {faker.catch_phrase()} 🎧 Indie music enthusiast discovering hidden tracks",
                f"{first_name} here - {faker.bs()} ✨ Music is my compass",
                f"Music explorer {first_name} 🎵 {faker.catch_phrase()}",
                f"I'm {first_name}, your friendly neighborhood indie scout 🔍 {faker.bs()}"
            ],
            'coffee_enthusiast': [
                f"Coffee shop connoisseur {first_name} ☕🎵 {faker.catch_phrase()}",
                f"Hi! I'm {first_name} - {faker.bs()} ☕ Every brew deserves its perfect playlist",
                f"{first_name} here, blending coffee culture with musical discovery 🎶 {faker.catch_phrase()}",
                f"Barista by day, music curator by heart ☕ I'm {first_name}, {faker.bs()}"
            ],
            'venue_hopper': [
                f"Live music lover {first_name} {faker.catch_phrase()} 🎤 If there's a stage, I've probably been there",
                f"Concert enthusiast {first_name} 🎸 {faker.bs()}",
                f"I'm {first_name}, your guide to the best live music experiences 🎵 {faker.catch_phrase()}",
                f"Music venue explorer {first_name} 🎭 {faker.bs()}"
            ],
            'campus_curator': [
                f"Student life soundtrack curator {first_name} 📚🎶 {faker.catch_phrase()}",
                f"College student {first_name} here! 🎓 {faker.bs()}",
                f"Campus music guide {first_name} 📖 {faker.catch_phrase()}",
                f"Student {first_name} {faker.bs()} 🎵 Music for every mood and moment"
            ],
            'city_wanderer': [
                f"Urban explorer {first_name} {faker.catch_phrase()} 🏙️🎵 Every street corner has a soundtrack",
                f"City guide {first_name} here! 🚶‍♀️ {faker.bs()}",
                f"I'm {first_name}, your musical tour guide through the city 🗺️ {faker.catch_phrase()}",
                f"Urban music explorer {first_name} 🌆 {faker.bs()}"
            ],
            'music_historian': [
                f"Music historian {first_name} {faker.catch_phrase()} 📻 Every song tells a story",
                f"I'm {first_name}, {faker.bs()} 🎼 Bridging generations through great music",
                f"Musical storyteller {first_name} 📚 {faker.catch_phrase()}",
                f"Heritage music curator {first_name} 🎵 {faker.bs()}"
            ],
            'genre_specialist': [
                f"Genre specialist {first_name} {faker.catch_phrase()} 🎼 Expert in electronic, ambient, and experimental sounds",
                f"Music analyst {first_name} here! 🔬 {faker.bs()}",
                f"I'm {first_name}, your guide through the complex world of musical genres 🎵 {faker.catch_phrase()}",
                f"Genre explorer {first_name} 🎧 {faker.bs()}"
            ],
            'local_guide': [
                f"Local music guide {first_name} 🗺️🎵 {faker.catch_phrase()}",
                f"Hometown hero {first_name} here! 🏠 {faker.bs()}",
                f"I'm {first_name}, your friendly neighborhood music ambassador 🎶 {faker.catch_phrase()}",
                f"Community music curator {first_name} 🤝 {faker.bs()}"
            ]
        }

        # Get templates for the persona type, fallback to city_wanderer if not found
        templates = bio_templates.get(persona_type, bio_templates['city_wanderer'])

        # Select a random template
        return random.choice(templates)

    def generate_user_profile(self, persona_type: str, preferred_genres: List[str],
                            preferred_locations: List[str], assigned_cities: List[str] = None) -> Dict[str, str]:
        """
        Generate a complete user profile with diverse, realistic information using Faker.

        Args:
            persona_type: The curator persona type
            preferred_genres: List of preferred music genres
            preferred_locations: List of preferred location types
            assigned_cities: Optional list of assigned cities

        Returns:
            Dictionary containing user profile information
        """
        # Generate diverse name
        first_name, last_name = self.generate_diverse_name()

        # Generate realistic username
        username = self.generate_realistic_username(first_name, last_name)

        # Generate unique avatar using ProfilePictureService
        unique_id = f"{username}_{persona_type}_{random.randint(1000, 9999)}"
        # Generate random gender for diversity
        gender = random.choice(['male', 'female', None])
        avatar_url = self.profile_pic_service.generate_realistic_avatar_url(unique_id, gender)

        # Generate personalized bio
        bio = self.generate_bio(persona_type, first_name)

        # Generate realistic email using Faker
        email_providers = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com', 'protonmail.com']
        email_provider = random.choice(email_providers)
        email = f"{username}@{email_provider}"

        return {
            'username': username,
            'first_name': first_name,
            'last_name': last_name,
            'email': email,
            'bio': bio,
            'profile_pic': avatar_url,
            'persona_type': persona_type,
            'preferred_genres': preferred_genres,
            'preferred_locations': preferred_locations,
            'assigned_cities': assigned_cities or []
        }

    def ensure_username_uniqueness(self, username: str, existing_usernames: set) -> str:
        """
        Ensure username uniqueness by adding suffixes if needed.

        Args:
            username: Proposed username
            existing_usernames: Set of existing usernames to check against

        Returns:
            Unique username
        """
        original_username = username
        counter = 1

        while username in existing_usernames:
            username = f"{original_username}{counter}"
            counter += 1

            # Prevent infinite loops
            if counter > 1000:
                username = f"{original_username}_{random.randint(1000, 9999)}"
                break

        return username
